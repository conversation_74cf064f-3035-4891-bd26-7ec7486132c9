// written by Ratnadeep

class financeHostelRateController {
    /*@ngInject*/
    constructor($http, $filter, $scope, pouchDB, $translate, $translatePartialLoader,
        $state, $stateParams, User, $anchorScroll, RestangularENMS, RestangularCDN, blockUI) {
        this.$http = $http;
        this.$filter = $filter;
        this.$scope = $scope;
        this.pouchDB = pouchDB;
        this.$translate = $translate;
        this.$translatePartialLoader = $translatePartialLoader;
        this.state = $state;
        this.stateParams = $stateParams;
        this.user = User;
        this.blockUI = blockUI;
        this.loadUrl = "https://staging.skillmissionassam.org/nw/";
        this.hostelAreaType = {};

        this.currentForm = {
            status: "",
            message: ""
        };

        this.hostelAreaTypeName = "";
        this.hostelAreaTypeCode = "";
        this.ratePerDay = "";
        // this.createHostelAreaType();
        this.getHostelAreaTypeList();
    }


    //Pagination and Search 
    // load(newPageNumber, oldPageNumber) {
    //   let postParam = {};
    //   postParam.q = this.currentForm.q;
    //   postParam.pageSize = this.currentForm.pageSize;
    //   postParam.currentPage = newPageNumber;
    //   postParam.hostelAreaTypeName  = this.currentForm.hostelAreaTypeName
    //   this.loadRest.post({ "postParam": postParam }) 
    //     .then(function (data) {
    //       if (data !== undefined && data !== null && !angular.equals({}, data) && data.status === "success") {
    //         this.currentForm.objArr = data.objArr;
    //         this.currentForm.roleArr = data.roleArr;
    //         this.currentForm.subModuleArr = data.subModuleArr;
    //         this.currentForm.jvLabel = data.jvLabel
    //         this.currentForm.totalItems = data.totalItems;
    //         this.currentForm.currentPage = data.currentPage;
    //         /********************************************************/
    //       } else {
    //         this.currentForm.status = "error";
    //         this.currentForm.message = data.message;
    //         this.currentForm.objArr = [];
    //         this.$anchorScroll();
    //       }
    //     }.bind(this.self));
    // }
    clear(newPageNumber, oldPageNumber) {
      this.currentForm.q = "";
      this.currentForm.pageSize = 10;
      this.currentForm.message = "";
      this.currentForm.status = "init";
      this.load(newPageNumber, oldPageNumber);
    }

    getHostelAreaTypeList(newPageNumber, newPageSize, search ) {
        this.currentPage = newPageNumber || this.currentPage || 1;
        this.pageSize = newPageSize || this.pageSize || 10;
        let postParam =  {
           currentPage: this.currentPage, 
            pageSize:this.pageSize,
            search : search ,
        };
        this.$http.post(this.loadUrl + "iams/app1/v1/IamsServer/rateList/get", { postParam: postParam }).then(response => { 
            console.log(postParam);
            this.totalItems = response.data.count;
            // console.log(this.totalItems);
            this.hostelAreaType = response.data.objArr.map(item => {
                return {
                    hostelAreaTypeId: item.hostelAreaTypeId,
                    areaTypeName: item.areaTypeName,                    
                    areaTypeCode: item.areaTypeCode,
                    selectedType: item.selectedType,
                    ratePerDay: item.ratePerDay,
                    status: item.status,
                }
                
                
                
            });
            // console.log(this.hostelAreaType.length);
        });

        // console.log(postParam);


        
    } 
searchHostelAreaType (search)
    {
        this.getHostelAreaTypeList(this.currentPage, this.pageSize, search);
    }
    
    createHostelAreaType() {
        // this.currentForm.message = "";
        this.blockUI.start();
        let postParam = {}
        postParam = {
           selectedType: this.selectedType,
           areaTypeName: this.hostelAreaTypeName,
           areaTypeCode: this.hostelAreaTypeCode,
           ratePerDay: this.ratePerDay
        };
        console.log(postParam);
        this.$http.post(this.loadUrl + "iams/app1/v1/IamsServer/rateList/save", { postParam: postParam }).then(response => {
            console.log(response);
            this.selectedType = "";
            this.hostelAreaTypeName = "";
            this.hostelAreaTypeCode = "";
            this.ratePerDay = "";
            this.currentForm.status = response.data.status;
            this.currentForm.message = response.data.message;
            this.getHostelAreaTypeList();
        }).catch(error => {
                this.currentForm.status = "error";
                this.currentForm.message = error.data.message;
              
        }).finally(() => {
            this.blockUI.stop();
        })


    } 

    updateHostelAreaTypeStatus(hostelAreaTypeCode, status) {

    this.blockUI.start();
    const newStatus = status === 'Enabled' ? 1 : 0;
    const postParam = {
      status: newStatus,
      hostelAreaTypeId: hostelAreaTypeCode
    };
    console.log('postParam', postParam);
   this.$http.post(this.loadUrl + "iams/app1/v1/IamsServer/rateList/update", { postParam: postParam })
    .then(response => {
      if (response.data && response.data.status === 'success') {
        this.currentForm.status = 'success';
        this.getHostelAreaTypeList();
      } else {
        this.currentForm.status = 'error';
        this.currentForm.message = response.data.message
      }
    })
    .catch(error => {
      console.error('Error updating status:', error);
      this.currentForm.status = 'error';
      this.currentForm.message = response.data.message;
    })
    .finally(() => {
      this.blockUI.stop();
    });
  }



    cancel() {
        this.hostelAreaTypeName = "";
        this.hostelAreaTypeCode = "";
        this.ratePerDay = "";
    }
}   

export default financeHostelRateController;