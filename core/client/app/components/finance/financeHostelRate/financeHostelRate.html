<div class="row">
    <!--Col-lg Begins-->
    <div class="col-lg-12">
        <!--Form Begins-->
        <form class="form-horizontal">
            <!--Card Begins-->
            <div class="card ">
                <!--Form Card Heading Begins-->
                <div class="card-head style-primary" ng-if="vm.view !== 3">
                    <header>Add Hostel Rate</header>
                </div>
                <div class="card-body media-screen"
                    ng-if="vm.currentForm.status==='error' || vm.currentForm.status==='success'">
                    <div class="form-group">
                        <div class="alert alert-callout alert-danger" ng-if="vm.currentForm.status==='error'">
                            <strong class="text-danger text-bold text-lg">Error </strong>
                            <span ng-bind="vm.currentForm.message"></span>
                            <button class="btn btn-primary"
                                ng-click="vm.currentForm.status=''; vm.load()">Close</button>
                        </div>
                        <div class="alert alert-callout alert-success" ng-if="vm.currentForm.status==='success'">
                            <strong class="text-success text-bold text-lg">Success </strong>
                            <span ng-bind="vm.currentForm.message"></span>
                            <button class="btn btn-primary"
                                ng-click="vm.currentForm.status=''; vm.load()">Close</button>
                        </div>

                    </div>
                </div>

                <div class="card-body media-screen">

                    <div class="form-group">
                        <label class="col-sm-3 control-label required">Type</label>
                        <div class="col-sm-8">
                            <select class="form-control" ng-model="vm.selectedType">
                                <option value="" disabled selected>Select Type</option>
                                <option value="Fixed">Fixed</option>
                                <option value="Area">Area Type</option>
                            </select>
                        </div>
                    </div>


                    <div class="form-group" ng-if="vm.selectedType === 'Area'">
                        <label class="col-sm-3 control-label required">Hostel Area Type Name</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" ng-model="vm.hostelAreaTypeName" />
                        </div>
                    </div>
                    <div class="form-group" ng-if="vm.selectedType === 'Area'">
                        <label class="col-sm-3 control-label required">Hostel Area Type Code</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" ng-model="vm.hostelAreaTypeCode" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label required">Rate (Per Day)</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" ng-model="vm.ratePerDay" />
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-offset-3 col-sm-9">
                            <button class="btn btn-primary" ng-click="vm.createHostelAreaType()">Submit</button>
                            <button class="btn btn-primary" ng-click="vm.cancel()">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>



            <div class="card">
                <div class="card-head style-primary">
                    <header>Hostel Area Type List</header>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <span class="text-lg text-bold text-primary">Total : <span ng-bind="vm.totalItems"></span>
                        </span>&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="text-lg text-bold text-primary">Page: <span ng-bind="vm.currentPage"></span>
                        </span>&nbsp;&nbsp;&nbsp;&nbsp;
                    </div>

                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <input type="number" min="1" max="100" ng-model="vm.pageSize" id="itemsPerPage"
                                    class="form-control" placeholder="Items Per Page">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <input ng-model="vm.search" ng-change="vm.searchHostelAreaType(vm.search)"
                                    placeholder="Search by Hostel Area Type Name" class="form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <button ng-click="vm.load(vm.currentForm.currentPage,vm.currentForm.currentPage)"
                                    class="btn btn-raised btn-primary btn-sm">
                                    <i class="fa fa-search"></i>Go
                                </button>
                                <button ng-click="vm.clear(vm.currentForm.currentPage,vm.currentForm.currentPage)"
                                    class="btn btn-raised btn-primary btn-sm">
                                    <i class="fa fa-eraser"></i>Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body media-screen">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <!-- <th>Hostel Area Type Id</th> -->
                                    <th>Hostel Area Type Name</th>
                                    <th>Hostel Area Type Code</th>
                                    <th> Fixed /Area </th>
                                    <th>Rate (Per Day)</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr dir-paginate="item in vm.hostelAreaType | itemsPerPage: vm.pageSize track by $index"
                                    total-items="vm.totalItems" current-page="vm.currentPage"
                                    items-per-page="vm.pageSize">
                                    <td>{{$index + 1 + (vm.currentPage - 1) * vm.pageSize}}</td>
                                    <!-- <td>{{item.hostelAreaTypeId}}</td> -->
                                    <td>{{item.areaTypeName}}</td>
                                    <td>{{item.areaTypeCode}}</td>
                                    <td>{{item.selectedType}}</td>
                                    <td>{{item.ratePerDay}}</td>
                                    <td>{{item.status}}</td>
                                    <td>
                                        <button class="btn btn-primary"
                                            ng-click="vm.updateHostelAreaTypeStatus(item.hostelAreaTypeId ,'Enabled')"
                                            ng-disabled="item.status==='Enabled'">Enable</button>
                                        <button class="btn btn-danger"
                                            ng-click="vm.updateHostelAreaTypeStatus(item.hostelAreaTypeId ,  'Disabled')"
                                            ng-disabled="item.status==='Disabled'">Disable</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div>
                            <div ng-if="vm.hostelAreaType.length > 0">
                                <dir-pagination-controls boundary-links="true" template-url="dirPagination.tpl.html"
                                    on-page-change="vm.getHostelAreaTypeList(newPageNumber, vm.pageSize)"
                                    current-page="vm.currentPage" total-items="vm.totalItems"
                                    items-per-page="vm.pageSize">
                                </dir-pagination-controls>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    </form>
</div>
</div>