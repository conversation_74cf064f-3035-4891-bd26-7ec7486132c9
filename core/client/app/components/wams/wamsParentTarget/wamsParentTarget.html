<!--Row Begins-->
<div class="row">
  <!--Col-lg Begins-->
  <div class="col-lg-12">
    <!--Form Begins-->
    <form class="form-horizontal">
      <!--Card Begins-->
      <div class="card">

        <!--Form Card Heading Begins-->
        <div class="card-head style-primary">
          <header>Parent Target</header>
        </div>
        <!--Form Card Heading Ends-->

        <!--Alerts-->
        <div class="card-body">
          <!--Error -->
          <div class="form-group">
            <div class="alert alert-callout alert-danger" ng-if="vm.currentForm.status==='error'">
              <strong class="text-danger text-bold text-lg">Error!!!</strong>
              <span ng-bind="vm.currentForm.message"></span>
            </div>
          </div>

          <!--Success -->
          <div class="form-group">
            <div class="alert alert-callout alert-success" ng-if="vm.currentForm.status==='success'">
              <strong class="text-success text-bold text-lg">Success!!!</strong>
              <span ng-bind="vm.currentForm.message"></span>
            </div>
          </div>
        </div>

        <!--Form Card Body Begins-->
        <div class="card-body"
          ng-if="vm.currentForm.objAddFlag || vm.currentForm.objUpdateFlag || vm.currentForm.objViewFlag">
          <!-- Scheme -->
          <div class="form-group required">
            <label class="col-sm-2 control-label text-bold">Scheme Name
            </label>
            <div class="col-sm-8">
              <select class="form-control"
                ng-options="scheme.schemeId as scheme.schemeName for scheme in vm.currentForm.schemeArr"
                ng-model="vm.currentForm.obj.schemeId" ng-disabled="vm.currentForm.objViewFlag">
                <option value="">Select the Scheme</option>
              </select>
            </div>
          </div>
          <!-- Funding Name -->
          <div class="form-group required">
            <label class="col-sm-2 control-label text-bold">Funding Entity
            </label>
            <div class="col-sm-8">
              <select class="form-control"
                ng-options="fundingEntity.fundingEntityId as fundingEntity.fundingEntityName for fundingEntity in vm.currentForm.fundingEntityArr"
                ng-model="vm.currentForm.obj.fundingEntityId" ng-disabled="vm.currentForm.objViewFlag">
                <option value="">Select the Funding Entity</option>
              </select>
            </div>
          </div>
          <!-- Pattern -->
          <div class="form-group required">
            <label class="col-sm-2 control-label text-bold">Pattern
            </label>
            <div class="col-sm-8">
              <select class="form-control"
                ng-options="pattern.patternId as pattern.pattern for pattern in vm.currentForm.patternArr"
                ng-model="vm.currentForm.obj.patternId" ng-disabled="vm.currentForm.objViewFlag">
                <option value="">Select the Pattern</option>
              </select>
            </div>
          </div>



          <!-- Fund -->
          <div class="form-group required">
            <label class="col-sm-2 control-label text-bold">Fund
            </label>
            <div class="col-sm-8">
              <select class="form-control" ng-options="fund.fundId as fund.fundName for fund in vm.currentForm.fundArr"
                ng-model="vm.currentForm.obj.fundId" ng-disabled="vm.currentForm.objViewFlag">
                <option value="">Select Fund</option>
              </select>
            </div>
          </div>
          <!--ParentTarget-->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="ParentTargetName">Parent Target Name
            </label>
            <div class="col-sm-8">
              <input type="text" class="form-control" ng-model="vm.currentForm.obj.parentTargetName"
                placeholder="Enter Parent Target Name" id="parentTargetName" ng-disabled="vm.currentForm.objViewFlag">
            </div>
          </div>
          <!--ParentTarget-->
          <div class="form-group required">
            <label class="col-sm-2 control-label">Sanction Target Check</label>
            <div class="col-sm-6">
              <label class="radio-inline radio-styled">
                <input type="radio" ng-model="vm.currentForm.obj.sanctionTargetCheck" ng-value="1"
                  ng-disabled="vm.currentForm.objViewFlag" ng-change="vm.onSanctionTargetChange()">
                <span>Yes</span>
              </label>
              <label class="radio-inline radio-styled">
                <input type="radio" ng-model="vm.currentForm.obj.sanctionTargetCheck" ng-value="0"
                  ng-disabled="vm.currentForm.objViewFlag" ng-change="vm.onSanctionTargetChange()">
                <span>No</span>
              </label>
            </div>
          </div>


          <div class="form-group required" ng-if="vm.currentForm.obj.sanctionTargetCheck">
            <label class="col-sm-2 control-label text-bold">Sanction Order No.
            </label>
            <div class="col-sm-8">
              <select class="form-control"
                ng-options="sanctionOrder.sanctionOrderNo as sanctionOrder.sanctionOrderNo for sanctionOrder in vm.currentForm.sanctionOrderArr"
                ng-model="vm.currentForm.obj.sanctionOrderNo" ng-disabled="vm.currentForm.objViewFlag"
                ng-change="vm.onSanctionOrderChange(vm.currentForm.obj.sanctionOrderNo)">
                <option value="">Select Sanction Order</option>
              </select>
            </div>
          </div>
          <div class="form-group required" ng-if="!vm.currentForm.obj.sanctionTargetCheck">
            <label class="col-sm-2 control-label">Sanction Order No.
            </label>
            <div class="col-sm-8">
              <input type="text" class="form-control" ng-model="vm.currentForm.obj.sanctionOrderNo"
                id="sanctionOrderNumber" ng-disabled="vm.currentForm.objViewFlag" placeholder="Enter Sanction Order">
              <div class="form-control-line"></div>
            </div>
          </div>

          <!--Sanction Date-->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="ParentTargetDate">Date Of Sanction
            </label>
            <div class="col-sm-8">
              <input type="text" class="form-control" ng-model="vm.currentForm.obj.sanctionDate" id="sanctionDate"
                ng-disabled="vm.currentForm.objViewFlag" placeholder="DD/MM/YYYY">
              <div class="form-control-line"></div>
            </div>
          </div>

          <!-- Residential Target -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="ParentTargetName">Residential Target
            </label>
            <div class="col-sm-8">
              <input type="text" class="form-control" ng-model="vm.currentForm.obj.residentialTarget"
                id="ParentTargetName" ng-disabled="vm.currentForm.objViewFlag || vm.currentForm.obj.sanctionTargetCheck"
                ng-change="vm.calculateTotalTarget()" placeholder="Enter Residential Target">
              <div class="form-control-line"></div>
            </div>
          </div>

                <div class="form-group required">
                        <label class="col-sm-2 control-label required">Hostel Area Type</label>
                        <div class="col-sm-8">
                            <select class="form-control" ng-model="vm.selectedType" ng-disabled="vm.currentForm.obj.residentialTarget<=0 || !vm.currentForm.obj.residentialTarget ">
                                <option value="" disabled selected>Select Type</option>
                                <option value="Fixed">Fixed</option>
                                <option value="Area">Area Type</option>
                            </select>
                        </div>
                    </div>

          <!-- Hostel Rate-->
          <div class="form-group required " ng-if="vm.selectedType === 'Area'">
            <label class="col-sm-2 control-label text-bold">Hostel Rate(Area)</label>
            <div class="col-sm-8">
              <div class="checkbox-group" ng-disabled="vm.currentForm.objViewFlag" style="max-height: 100px; overflow-y: auto; border: 2px solid #e0e0e0; padding: 15px; border-radius: 6px; background-color: #fafafa; box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);">
                <div class="checkbox-item" ng-repeat="rate in vm.currentForm.hostelRateArr" style="margin-bottom: 10px; padding: 5px; border-radius: 3px; transition: background-color 0.2s;">
                  <label class="checkbox-label" style="display: flex; align-items: center; cursor: pointer; font-size: 10px;">
                    <input type="checkbox" 
                           ng-model="rate.selected" 
                           ng-change="vm.updateHostelRateSelection()"
                           ng-disabled="vm.currentForm.objViewFlag"
                           style="margin-right: 10px; transform: scale(1.2);">
                           <span class="checkbox-text">₹{{rate.hostelRate}} ({{rate.hostelRateName}})</span>

                  </label>
                </div>
              </div>
            </div>
          </div>


          <div class="form-group required " ng-if="vm.selectedType === 'Fixed'"> 
            <label class="col-sm-2 control-label text-bold">Hostel Rate(Fixed)</label>
            <div class="col-sm-8">
              <select class="form-control" ng-disabled="vm.currentForm.objViewFlag"
                ng-options="rate.hostelRate for rate in vm.currentForm.hostelRateArrFixed"
                ng-model="vm.selectedFixedRate">
                <option value="">Select Rate</option>
              </select>
            </div>
          </div>



          <!-- Non-Residential Target -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="ParentTargetName">Non-Residential Target
            </label>
            <div class="col-sm-8">
              <input type="text" class="form-control" ng-model="vm.currentForm.obj.nonResidentialTarget"
                id="ParentTargetName" ng-disabled="vm.currentForm.objViewFlag || vm.currentForm.obj.sanctionTargetCheck"
                ng-change="vm.calculateTotalTarget()" placeholder="Enter Non-Residential Target">
              <div class="form-control-line"></div>
            </div>
          </div>
          <!-- Total Target -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="ParentTargetName">Total Target
            </label>
            <div class="col-sm-8">
              <input type="text" class="form-control" id="ParentTargetName" ng-model="vm.currentForm.obj.totalTarget"
                placeholder="Total Target" disabled>
              <div class="form-control-line"></div>
            </div>
          </div>
          <!-- Target Start Date -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="ParentTargetDate">Target Start Date
            </label>
            <div class="col-sm-8">
              <input type="text" class="form-control" ng-model="vm.currentForm.obj.targetStartDate"
                id="ParentTargetDate" ng-disabled="vm.currentForm.objViewFlag" placeholder="DD/MM/YYYY">
              <div class="form-control-line"></div>
            </div>
          </div>
          <!-- Target End Date -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="ParentTargetDate">Target End Date
            </label>
            <div class="col-sm-8">
              <input type="text" class="form-control" ng-model="vm.currentForm.obj.targetEndDate" id="ParentTargetDate"
                ng-disabled="vm.currentForm.objViewFlag" placeholder="DD/MM/YYYY">
              <div class="form-control-line"></div>
            </div>
          </div>
          <!--SubModule Document-->
          <div class="form-group">
            <label class="col-sm-2 control-label">Upload Document</label>
            <div class="col-sm-10">
              <div class="form-inline">
                <div class="form-group row">
                  <button ngf-select="vm.upload(vm.currentForm.docPath,'docPath',vm.currentForm.docSizeInBytes,
                    vm.currentForm.docPath,vm.currentForm.docUnit,vm.currentForm.obj)" name="docPath"
                    ngf-pattern="vm.currentForm.docMIME" ngf-accept="vm.currentForm.docMIME"
                    ng-model="vm.currentForm.docPath">Select</button>
                  <p class="nw-para form-text nw-nw-text-muted">
                    Must be less than <span ng-bind="vm.currentForm.docSize"></span>
                    <span ng-bind="vm.currentForm.docUnit"></span>(<span ng-bind="vm.currentForm.docMIME"></span>)
                  </p>
                </div>
                <div class="form-group">
                  <a ng-href="{{vm.currentForm.obj.docPath}}" alt="Preview" class="medium-thumbnail"
                    ng-show="vm.isDocumentAvailable('docPath')" target="_blank">Download</a>
                </div>
                <div class="form-group row">
                  <button ng-click="vm.clearDocument('docPath',vm.currentForm.obj)"
                    ng-show="vm.isDocumentAvailable('docPath')">Delete
                  </button>
                </div>
              </div>
            </div>
          </div>

        </div>
        <!--Form Card Body Ends(Add/Update/View)-->

        <!--Form Card Footer Begins-->
        <div class="card-actionbar">
          <div class="card-actionbar-row">
            <button type="button" class="btn btn-raised btn-success ink-reaction" ng-click="vm.save()"
              ng-if="vm.currentForm.objAddFlag">
              Save
            </button>
            <button type="button" class="btn btn-raised btn-success ink-reaction" ng-click="vm.update()"
              ng-if="vm.currentForm.objUpdateFlag">
              Update
            </button>
            <button type="button" class="btn btn-raised btn-primary ink-reaction" ng-click="vm.cancel()"
              ng-if="vm.currentForm.objAddFlag || vm.currentForm.objUpdateFlag || vm.currentForm.objViewFlag">
              Cancel
            </button>
          </div>
        </div>
        <!--Form Card Footer Ends-->

        <!--Form Card Body Begins(List)-->
        <div class="card-body"
          ng-if="!vm.currentForm.objAddFlag && !vm.currentForm.objUpdateFlag && !vm.currentForm.objViewFlag && vm.currentForm.objArr.length > 0">

          <div class="form-group">
            <span class="text-lg text-bold text-primary">Total Items : <span ng-bind="vm.currentForm.totalItems"></span>
            </span>&nbsp;&nbsp;&nbsp;&nbsp;
            <span class="text-lg text-bold text-primary">Page: <span ng-bind="vm.currentForm.currentPage"></span>
            </span>
            <button class="btn btn-raised btn-sm btn-primary" ng-click="vm.add()"
              ng-if="vm.currentForm.access.addTargetVisible==1"><i class="fa fa-plus"></i>
              Add Parent Target
            </button>
          </div>
          <!--Search/Filter-->
          <div class="row">
            <div class="col-sm-4">
              <div class="form-group">
                <input type="number" min="1" max="100" ng-model="vm.currentForm.pageSize" id="itemsPerPage"
                  class="form-control" placeholder="Items Per Page">
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <input ng-model="vm.currentForm.q" id="search" placeholder="Search On Parent Target"
                  class="form-control">
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <button ng-click="vm.load(vm.currentForm.currentPage,vm.currentForm.currentPage)"
                  class="btn btn-raised btn-primary btn-sm">
                  <i class="fa fa-search"></i>Go
                </button>
                <button ng-click="vm.clear(vm.currentForm.currentPage,vm.currentForm.currentPage)"
                  class="btn btn-raised btn-primary btn-sm">
                  <i class="fa fa-eraser"></i>Clear
                </button>
              </div>
            </div>
          </div>

          <!--Pagination-->
          <div>
            <dir-pagination-controls boundary-links="true" template-url="dirPagination.tpl.html"
              on-page-change="vm.load(newPageNumber, oldPageNumber)">
            </dir-pagination-controls>
          </div>

          <div class="table-responsive">
            <table class="table table-hover table-banded table-condensed">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Parent Target Name</th>
                  <th>Funding Entity</th>
                  <th ng-if="vm.currentForm.access.viewTargetVisible==1">View</th>
                  <th ng-if="vm.currentForm.access.editTargetVisible==1">Edit</th>
                  <th ng-if="vm.currentForm.access.deleteTargetVisible==1">Delete</th>
                </tr>
              </thead>
              <tbody
                dir-paginate="rowObj in vm.currentForm.objArr | itemsPerPage: vm.currentForm.pageSize track by rowObj.parentTargetId"
                total-items="vm.currentForm.totalItems" current-page="vm.currentForm.currentPage">
                <tr>
                  <td ng-bind="rowObj.sNo"></td>
                  <td ng-bind="rowObj.parentTargetName"></td>
                  <td ng-bind="rowObj.fundingEntityName"></td>
                  <td ng-if="vm.currentForm.access.viewTargetVisible==1">
                    <button ng-click="vm.view(rowObj)" class="btn btn-raised btn-success btn-sm">
                      View
                    </button>
                  </td>
                  <td ng-if="vm.currentForm.access.editTargetVisible==1">
                    <button ng-click="vm.edit(rowObj)" class="btn btn-raised btn-info btn-sm">
                      Edit
                    </button>
                  </td>
                  <td ng-if="vm.currentForm.access.deleteTargetVisible==1">
                    <button ng-click="vm.modalBoxMessage(rowObj,'DELETE')" class="btn btn-raised btn-danger btn-sm"
                      data-toggle="modal" data-target="#parentTargetModal">
                      Delete
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!--Pagination-->
          <div>
            <dir-pagination-controls boundary-links="true" template-url="dirPagination.tpl.html"
              on-page-change="vm.load(newPageNumber, oldPageNumber)">
            </dir-pagination-controls>
          </div>
        </div>
        <!--Form Card Body Ends(List)-->

        <!--Delete Modal-->
        <div class="modal fade" id="parentTargetModal" tabindex="-1" role="dialog" aria-labelledby="simpleModalLabel"
          aria-hidden="true" style="display: none;">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×
                </button>
                <h4 class="modal-title" id="simpleModalLabel" ng-if="vm.currentForm.modalBoxMessageAction==='DELETE'">
                  Delete Parent Target
                </h4>
              </div>
              <div class="modal-body text-danger text-bold">
                <ul ng-if="vm.currentForm.modalBoxMessageBody==='delete'">
                  <li>You Are Deleting Parent Target</li>
                  <li>Do You Still Want To Continue?</li>
                </ul>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel
                </button>
                <button type="button" class="btn btn-danger" ng-click="vm.delete()" data-dismiss="modal"
                  ng-if="vm.currentForm.modalBoxMessageAction==='DELETE'">Delete</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--Card Ends-->
    </form>
    <!--Form Ends-->
  </div>
  <!--Col-lg Ends-->
</div>
<!--Row Ends