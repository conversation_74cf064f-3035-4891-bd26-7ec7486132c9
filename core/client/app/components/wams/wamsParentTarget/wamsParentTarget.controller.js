/**
 * AmolJ
 */
class wamsParentTargetController {
  /*@ngInject*/
  constructor($filter, $scope, pouchDB, $translate, $translatePartialLoader,
    $state, $stateParams, User, $anchorScroll, RestangularWAMS, RestangularCDN) {
    this.self = this;
    this.$anchorScroll = $anchorScroll;
    this.loadRest = RestangularWAMS.all("parentTarget/get");
    this.saveRest = RestangularWAMS.all("parentTarget/save");
    this.updateRest = RestangularWAMS.all("parentTarget/update");
    this.deleteRest = RestangularWAMS.all("parentTarget/delete");
    this.CDNServerRest = RestangularCDN;

    this.currentForm = {};
    this.currentForm.status = "init";
    this.currentForm.message = "";
    this.currentForm.objAddFlag = false;
    this.currentForm.objUpdateFlag = false;
    this.currentForm.objViewFlag = false;

    this.currentForm.obj = {};
    this.currentForm.objArr = [];
    this.currentForm.access = {};

    //pagination related     
    this.currentForm.q = "";
    this.currentForm.pageSize = 10;
    this.currentForm.currentPage = 1;
    this.currentForm.totalItems = 0;
    this.currentForm.maxPageSize = 100;

    this.currentForm.organizationId = 'asdm';
    this.currentForm.businessUnitId = 'domainskilling';
    this.currentForm.projectId = 'finance';
    this.currentForm.docSize = 10;
    this.currentForm.docUnit = 'MB';
    this.currentForm.docMIME = '.pdf,.zip';
    this.currentForm.docSizeInBytes = 10000000;

    this.state = $state;
    this.stateParams = $stateParams;
    this.currentForm.moduleId = this.stateParams.moduleId;
    this.currentForm.subModuleId = this.stateParams.subModuleId;
    this.user = User;

    this.load(this.currentForm.currentPage, 0);
  }
  /********************************************************************************/
  /********************************************************************************/
  load(newPageNumber, oldPageNumber) {
    if (this.currentForm.pageSize !== undefined
      && this.currentForm.pageSize !== null
      && this.currentForm.pageSize > this.currentForm.maxPageSize) {
      this.currentForm.pageSize = this.currentForm.maxPageSize;
    }
    if (this.currentForm.pageSize === undefined
      || this.currentForm.pageSize === null) {
      this.currentForm.pageSize = this.currentForm.maxPageSize;
    }
    let postParam = {};
    postParam.q = this.currentForm.q;
    postParam.pageSize = this.currentForm.pageSize;
    postParam.currentPage = newPageNumber;
    postParam.moduleId = this.currentForm.moduleId;
    postParam.subModuleId = this.currentForm.subModuleId;
    this.loadRest.post({ "postParam": postParam })
      .then(function (data) {
        if (data !== undefined && data !== null && !angular.equals({}, data) && data.status === "success") {
          this.currentForm.objArr = data.objArr;
          this.currentForm.schemeArr = data.schemeArr;
          this.currentForm.sanctionOrderArr = data.sanctionOrderArr;
          this.currentForm.fundingEntityArr = data.fundingEntityArr;
          this.currentForm.fundArr = data.fundArr;
          this.currentForm.patternArr = data.patternArr;
          this.currentForm.hostelRateArr = data.hostelRateArrArea;
          this.currentForm.hostelRateArrFixed = data.hostelRateArrFixed;
          this.currentForm.access = data.access;
          //pagination       
          this.currentForm.totalItems = data.totalItems;
          this.currentForm.currentPage = data.currentPage;
        } else {
          this.currentForm.status = "error";
          this.currentForm.message = data.message;
          this.$anchorScroll();
        }
        // console.log(this.currentForm.hostelRateArr);
      }.bind(this.self));
  }
  /*********************************************************************/
  /*********************************************************************/
  add() {
    this.currentForm.objAddFlag = true;
    this.currentForm.objUpdateFlag = false;
    this.currentForm.objViewFlag = false;
    this.currentForm.status = "init";
    this.currentForm.message = "";
    this.currentForm.obj = {};
  }
  /*********************************************************************/
  /*********************************************************************/
  view(obj) {
    this.currentForm.objAddFlag = false;
    this.currentForm.objUpdateFlag = false;
    this.currentForm.objViewFlag = true;
    this.currentForm.status = "init";
    this.currentForm.message = "";
    this.currentForm.obj = angular.copy(obj);
  }
  /*********************************************************************/
  /*********************************************************************/
  edit(obj) {
    this.currentForm.objAddFlag = false;
    this.currentForm.objUpdateFlag = true;
    this.currentForm.objViewFlag = false;
    this.currentForm.status = "init";
    this.currentForm.message = "";
    this.currentForm.obj = angular.copy(obj);
  }
  /*********************************************************************/
  /*********************************************************************/
  cancel() {
    this.currentForm.objAddFlag = false;
    this.currentForm.objUpdateFlag = false;
    this.currentForm.objViewFlag = false;
    this.currentForm.status = "init";
    this.currentForm.message = "";
    this.currentForm.obj = {};
    this.$anchorScroll();
  }
  /*********************************************************************/
  /*********************************************************************/
  clear(newPageNumber, oldPageNumber) {
    this.currentForm.q = "";
    this.currentForm.message = "";
    this.currentForm.status = "init";
    this.currentForm.pageSize = 10;
    this.load(newPageNumber, oldPageNumber);
  }
  /*********************************************************************/
  /*********************************************************************/
  save() {
    this.currentForm.status = "init";
    this.currentForm.message = "";
    
    let selectedHostelRates = [];
    this.selectedFixedRate = null; 
    if (this.selectedType === 'Area') {
      selectedHostelRates = (this.currentForm.hostelRateArr || [])
        .filter(rate => rate.selected)
        .map(rate => ({
          SelectedHostelRate: rate.hostelRate,
          selectedHostedId: rate.hostelRateId,
          rateType: 'Area'
        }));
    } else if (this.selectedType === 'Fixed'  && this.selectedFixedRate) {
      selectedHostelRates.push({
        SelectedHostelRate: this.selectedFixedRate.hostelRate,
        selectedHostedId: this.selectedFixedRate.hostelRateId,
        rateType: 'Fixed'
      });
    }
  
    this.currentForm.obj.selectedHostelRates = selectedHostelRates;
  
  

    let postParam = {};
    postParam.obj = this.currentForm.obj;
    postParam.pageSize = this.currentForm.pageSize;
    postParam.currentPage = this.currentForm.currentPage;
    postParam.moduleId = this.currentForm.moduleId;
    postParam.subModuleId = this.currentForm.subModuleId;
    console.log("Save Rest ",postParam);
    this.saveRest.post({ "postParam": postParam })
      .then(function (data) {
        if (data !== undefined && data !== null && !angular.equals({}, data)
          && data.status === "success") {
          if (data.objArr !== undefined && data.objArr !== null
            && data.objArr.length > 0) {
            this.currentForm.status = "success";
            this.currentForm.message = data.message;
            this.currentForm.objAddFlag = false;
            this.currentForm.objUpdateFlag = false;
            this.currentForm.objViewFlag = false;
            this.currentForm.obj = {};
            this.currentForm.objArr = data.objArr;
            this.$anchorScroll();
          } else {
            this.currentForm.status = "init";
            this.currentForm.message = "";
            this.currentForm.objAddFlag = false;
            this.currentForm.objUpdateFlag = false;
            this.currentForm.objViewFlag = false;
            this.currentForm.obj = {};
            this.currentForm.objArr = [];
            this.$anchorScroll();
          }
          this.currentForm.totalItems = data.totalItems;
          this.currentForm.currentPage = data.currentPage;
        } else {
          this.currentForm.status = "error";
          this.currentForm.message = data.message;
          this.$anchorScroll();
        }
      }.bind(this.self));
  }
  /*********************************************************************/
  /*********************************************************************/
  update() {
    // Collect selected rates from Area type only
    const selectedAreaRates = (this.currentForm.hostelRateArr || [])
      .filter(rate => rate.selected)
      .map(rate => ({
        SelectedAreaType: rate.hostelRateName || 'Area',
        SelectedHostelRate: rate.hostelRate,
        selectedHostedId: rate.hostelRateId
      }));
    
    // Set selected hostel rates with the required field names
    this.currentForm.obj.selectedHostelRates = selectedAreaRates;
    
    let postParam = {};
    postParam.obj = this.currentForm.obj;
    postParam.pageSize = this.currentForm.pageSize;
    postParam.currentPage = this.currentForm.currentPage;
    postParam.moduleId = this.currentForm.moduleId;
    postParam.subModuleId = this.currentForm.subModuleId;
    this.updateRest.post({ "postParam": postParam })
      .then(function (data) {
        if (data !== undefined && data !== null && !angular.equals({}, data)
          && data.status === "success") {
          if (data.objArr !== undefined && data.objArr !== null
            && data.objArr.length > 0) {
            this.currentForm.status = "success";
            this.currentForm.message = data.message;
            this.currentForm.objAddFlag = false;
            this.currentForm.objUpdateFlag = false;
            this.currentForm.objViewFlag = false;
            this.currentForm.obj = {};
            this.currentForm.objArr = data.objArr;
            this.$anchorScroll();
          } else {
            this.currentForm.status = "init";
            this.currentForm.message = "";
            this.currentForm.objAddFlag = false;
            this.currentForm.objUpdateFlag = false;
            this.currentForm.objViewFlag = false;
            this.currentForm.obj = {};
            this.currentForm.objArr = [];
            this.$anchorScroll();
          }
          this.currentForm.totalItems = data.totalItems;
          this.currentForm.currentPage = data.currentPage;
        } else {
          this.currentForm.status = "error";
          this.currentForm.message = data.message;
          this.$anchorScroll();
        }
      }.bind(this.self));
  }
  /*********************************************************************/
  /*********************************************************************/
  delete() {
    this.currentForm.status = "init";
    this.currentForm.message = "";
    let postParam = {};
    postParam.id = this.currentForm.toDelete;
    postParam.pageSize = this.currentForm.pageSize;
    postParam.currentPage = this.currentForm.currentPage;
    postParam.moduleId = this.currentForm.moduleId;
    postParam.subModuleId = this.currentForm.subModuleId;
    this.deleteRest.post({ "postParam": postParam })
      .then(function (data) {
        if (data !== undefined && data !== null && !angular.equals({}, data)
          && data.status === "success") {
          if (data.objArr !== undefined && data.objArr !== null
            && data.objArr.length > 0) {
            this.currentForm.status = "success";
            this.currentForm.message = data.message;
            this.currentForm.objAddFlag = false;
            this.currentForm.objUpdateFlag = false;
            this.currentForm.objViewFlag = false;
            this.currentForm.obj = {};
            this.currentForm.objArr = data.objArr;
            this.$anchorScroll();
          } else {
            this.currentForm.status = "init";
            this.currentForm.message = "";
            this.currentForm.objAddFlag = false;
            this.currentForm.objUpdateFlag = false;
            this.currentForm.objViewFlag = false;
            this.currentForm.obj = {};
            this.currentForm.objArr = [];
            this.$anchorScroll();
          }
          this.currentForm.totalItems = data.totalItems;
          this.currentForm.currentPage = data.currentPage;
        } else {
          this.currentForm.status = "error";
          this.currentForm.message = data.message;
          this.$anchorScroll();
        }
      }.bind(this.self));
  }
  /*********************************************************************/
  /*********************************************************************/
  modalBoxMessage(obj, action) {
    this.currentForm.modalBoxMessageBody = "";
    if (action === "DELETE") {
      this.currentForm.toDelete = obj.parentTargetId;
      this.currentForm.modalBoxMessageBody = "delete";
      this.currentForm.modalBoxMessageAction = action;
    }
  }
  /*********************************************************************/
  totalTarget() {
    this.currentForm.obj.totalTarget = 0;
    this.currentForm.obj.totalTarget = parseInt(this.currentForm.obj.nonResidentialTarget) + parseInt(this.currentForm.obj.residentialTarget);
    return this.currentForm.obj.totalTarget;
  }
  /*********************************************************************/
  onSanctionTargetChange() {
    this.currentForm.obj.residentialTarget = null;
    this.currentForm.obj.nonResidentialTarget = null;
    this.currentForm.obj.totalTarget = null;
    
    if (!this.currentForm.obj.sanctionTargetCheck) {
      this.currentForm.obj.sanctionOrderNo = "";
    }
  }
  onSanctionOrderChange(sanctionOrderNo) {
    for (let i = 0; i < this.currentForm.sanctionOrderArr.length; i++) {
      if (this.currentForm.sanctionOrderArr[i].sanctionOrderNo == sanctionOrderNo) {
        this.currentForm.obj.residentialTarget = this.currentForm.sanctionOrderArr[i].residentialTarget;
        this.currentForm.obj.nonResidentialTarget = this.currentForm.sanctionOrderArr[i].nonResidentialTarget;
        this.currentForm.obj.totalTarget = this.currentForm.sanctionOrderArr[i].totalTarget;
        break;
      }
    }
  }
  calculateTotalTarget() {
    let residentialTarget = this.currentForm.obj.residentialTarget ?
      parseInt(this.currentForm.obj.residentialTarget) : 0;
    let nonResidentialTarget = this.currentForm.obj.nonResidentialTarget ?
      parseInt(this.currentForm.obj.nonResidentialTarget) : 0;
    this.currentForm.obj.totalTarget = residentialTarget + nonResidentialTarget;
  }
  /*********************************************************************/
  clearDocument(imageNo, obj) {
    this.currentForm.status = "init";
    this.currentForm.message = "";
    this.currentForm[imageNo] = null;
    obj[imageNo] = null;
  }
  /*********************************************************************/
  /*********************************************************************/
  upload(file, imageNo, sizeInBytes, size, unit, obj) {
    this.currentForm.status = "init";
    this.currentForm.message = "";
    if (file) {
      if (file.size > sizeInBytes) {
        this.currentForm.status = "error";
        this.currentForm.message = "Maximum File Size Not Allowed";
        this.$anchorScroll();
      } else {
        var formData = new FormData();
        formData.append('file', file);
        this.CDNServerRest
          .all("uploadMultipart")
          .all(this.currentForm.organizationId)
          .all(this.currentForm.businessUnitId)
          .all(this.currentForm.projectId)
          .withHttpConfig({
            transformRequest: angular.identity,
          })
          .customPOST(formData, undefined, undefined,
            {
              'Content-Type': undefined
            })
          .then(function (data) {
            if (data !== undefined && data !== null && !angular.equals({}, data) && data.status) {
              obj[imageNo] = data.cdnResult;
            } else {
              this.currentForm.status = "error";
              this.currentForm.message = data.cdnResult;
              this.$anchorScroll();
            }
          }.bind(this.self))
          .catch(function (error) {
            this.currentForm.status = "error";
            this.currentForm.message = error.message;
            this.currentForm[imageNo + "InProgress"] = false;
          }.bind(this.self));
      }
    }
  }


  /*********************************************************************/
  isDocumentAvailable(imageNo) {
    if (this.currentForm.obj[imageNo] !== undefined
      && this.currentForm.obj[imageNo] !== null
      && this.currentForm.obj[imageNo].toString().trim().length > 0) {
      return true;
    } else {
      return false;
    }
  }
  /*********************************************************************/
  updateHostelRateSelection() {
    // This method is called when Area type checkboxes change
    console.log("Area rates selection updated");
  }
  /*********************************************************************/
}
export default wamsParentTargetController;
/*********************************************************************/
/*********************************************************************/