<div class="card card-outlined style-primary" id="candidateAddressScroll">
    <div class="card-head">
        <header><i class="fa fa-fw fa-tag"></i>Candidate Address Detail</header>
    </div>
    <div class="card-body">
        <div>
            <!--Error messages -->
            <div class="alert alert-callout alert-danger" role="alert" ng-if="vm.currentForm.addStatus === 'error'">
                <strong class="text-danger text-bold text-lg">Error!!!</strong>
                <h4 ng-bind="vm.currentForm.message"></h4>
                <p class="text-danger" ng-show="addressForm.resCountry.$error.required">Residential Country is required.</p>
                <p class="text-danger" ng-show="addressForm.resState.$error.required">Residential State is required.</p>
                <p class="text-danger" ng-show="addressForm.resDistrict.$error.required">Residential District is required.</p>
                <p class="text-danger" ng-show="addressForm.resRularUrbanOptions.$error.required">Residential Rural/Urban Type is required.</p>
                <p class="text-danger" ng-show="addressForm.resTaluka.$error.required">Residential Tehsil/Block is required.</p>
                <p class="text-danger" ng-show="addressForm.resCity.$error.required">Residential City Name is required.</p>
                <p class="text-danger" ng-show="addressForm.resAddress.$error.required">Residential Address is required.</p>
                <p class="text-danger" ng-show="addressForm.resPincode.$error.required">Residential Pincod is required.</p>
                <p class="text-danger" ng-show="addressForm.resNationality.$error.required">Residential Nationality is required.</p>
                <p class="text-danger" ng-show="addressForm.perCountry.$error.required">Permanent Country is required.</p>
                <p class="text-danger" ng-show="addressForm.perState.$error.required">Permanent State is required.</p>
                <p class="text-danger" ng-show="addressForm.perDistrict.$error.required">Permanent District is required.</p>
                <p class="text-danger" ng-show="addressForm.perRularUrbanOptions.$error.required">Permanent Rural/Urban Type is required.</p>
                <p class="text-danger" ng-show="addressForm.perTaluka.$error.required">Permanent Tehsil/Block is required.</p>
                <p class="text-danger" ng-show="addressForm.perCity.$error.required">Permanent City Name is required.</p>
                <p class="text-danger" ng-show="addressForm.perAddress.$error.required">Permanent Address is required.</p>
                <p class="text-danger" ng-show="addressForm.perPincode.$error.required">Permanent Pincod is required.</p>
                <p class="text-danger" ng-show="addressForm.perNationality.$error.required">Permanent Nationality is required.</p>
                <p class="text-danger" ng-show="addressForm.correspondenceOption.$error.required">Address of correspondence is required.</p>
            </div>
            <!--Success messages -->
            <div class="alert alert-callout alert-success" role="alert" ng-if="vm.currentForm.addStatus === 'success'">
                <strong class="text-success text-bold text-lg">Success!!!</strong>
                <h4 ng-bind="vm.currentForm.message"></h4>
            </div>
        </div>
        <form class="form-horizontal" name="addressForm" role="form">
            <p class="lead">
                <ins>Residential Address</ins>
            </p>
            <input ng-hide="true" ng-model="vm.currentForm.resAddressDetail.addressId" type="hidden" id="resAddressId" placeholder="">
            <input ng-hide="true" ng-model="vm.currentForm.resAddressDetail.addressType" type="hidden" id="resAddressId" placeholder="">
            <div class="form-group required">
                <label for="resCountry" class="col-sm-2 control-label required">Country</label>
                <div class="col-sm-10">
                    <select name="resCountry" id="resCountry" ng-options="country.vsCountryName for country in vm.currentForm.rescountryList track by country.pklCountryId" ng-model="vm.currentForm.resAddressDetail.country" class="form-control" ng-change="vm.loadResState(vm.currentForm.resAddressDetail.country)" required>
                    </select>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group required">
                <label for="resState" class="col-sm-2 control-label required">State</label>
                <div class="col-sm-10">
                    <select name="resState" id="resState" ng-options="state.vsStateName for state in vm.currentForm.resstateList track by state.pklStateId" ng-model="vm.currentForm.resAddressDetail.state" class="form-control" ng-change="vm.loadResDistrict(vm.currentForm.resAddressDetail.state)" required>
                    </select>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group required">
                <label for="resDistrict" class="col-sm-2 control-label required">District</label>
                <div class="col-sm-10">
                    <select name="resDistrict" id="resDistrict" ng-options="district.vcDistName for district in vm.currentForm.resdistrictList track by district.pkDistId" ng-model="vm.currentForm.resAddressDetail.district" class="form-control" ng-change="vm.loadResTaluka(vm.currentForm.resAddressDetail.district)" required>
                    </select>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group required">
                <label for="resRuralUrban" class="col-sm-2 control-label required">Rural/Urban </label>
                <div class="col-sm-10">
                    <label class="radio-inline radio-styled">
                        <input ng-model="vm.currentForm.resAddressDetail.areaType" type="radio" name="resRularUrbanOptions" value="RURAL" required> <span>Rural</span>
                    </label>
                    <label class="radio-inline radio-styled">
                        <input ng-model="vm.currentForm.resAddressDetail.areaType" type="radio" name="resRularUrbanOptions" value="URBAN" required><span> Urban</span>
                    </label>

                </div>
            </div>
            <!-- ******************************************************************************* -->
            <!-- for ulb and block -->
            <!-- ******************************************************************************* -->
                    <!--Block-->
                    <div class="form-group required" ng-if="vm.currentForm.resAddressDetail.areaType==='RURAL'">
                      <label class="col-sm-2 control-label">
                        <strong class="text-accent-dark text-bold  ">Block
                        </strong>
                      </label>
                      <div class="col-sm-10">
                        <select class="form-control" ng-options="taluka.pkTalId as taluka.vcTalName for taluka in vm.currentForm.restalukaList"
                          ng-model="vm.currentForm.resAddressDetail.talukaId"
                           ng-disabled ="vm.currentForm.resAddressDetail.state.vsStateName !== 'Assam'" 
                          >
                          <option value="">Select</option>
                        </select>
                        <div class="form-control-line"></div>
                      </div>
                    </div>

                    <div class="form-group required" ng-if="vm.currentForm.resAddressDetail.areaType==='URBAN'">
                      <label class="col-sm-2 control-label">
                        <strong class="text-accent-dark text-bold  ">ULB
                        </strong>
                      </label>
                      <div class="col-sm-10">
                        <select class="form-control" ng-options="ulb.ulbId as ulb.ulbName for ulb in vm.currentForm.resulbList"
                          ng-model="vm.currentForm.resAddressDetail.ulbId"
                           ng-disabled="vm.currentForm.resAddressDetail.state.vsStateName !=='Assam'"
                          >
                          <option value="">Select</option>
                        </select>
                        <div class="form-control-line"></div>
                      </div>
                    </div>

            <!-- ******************************************************************************* -->
            <!-- End of ulb and block -->
            <!-- ******************************************************************************* -->


            <!-- ******************************************************************************* -->
            <!-- Start of council and  assembly constituency -->
            <!-- ******************************************************************************* -->

                       <!-- Council constituency -->
                    <div class="form-group required" ng-if="vm.currentForm.accessObj.locasabhaConstituencyInvolved===1">
                      <label class="col-sm-2 control-label">
                        <strong class="text-accent-dark text-bold "> Council constituency
                        </strong>
                      </label>
                      <div class="col-sm-10">
                        <select class="form-control" ng-options="councilC.loksabhaId as councilC.loksabhaName for councilC in vm.currentForm.loksabhaArr"
                          ng-model="vm.currentForm.resAddressDetail.councilId" ng-change="vm.getAssemlyArr(vm.currentForm.resAddressDetail.councilId)"
                           ng-disabled="vm.currentForm.resAddressDetail.state.vsStateName !== 'Assam'"
                          >
                          <option value="">Select</option>
                        </select>
                        <div class="form-control-line"></div>
                      </div>
                    </div>

                    <!-- Assembly constituency -->
                    <div class="form-group required" ng-if="vm.currentForm.accessObj.assemblyConstituencyInvolved===1">
                      <label class="col-sm-2 control-label">
                        <strong class="text-accent-dark text-bold">Assembly constituency
                        </strong>
                      </label>
                      <div class="col-sm-10">
                        <select class="form-control" ng-options="assemblyC.assemblyId as assemblyC.assemblyName for assemblyC in vm.currentForm.assemblyArr"
                          ng-model="vm.currentForm.resAddressDetail.assemblyId"
                           ng-disabled="vm.currentForm.resAddressDetail.state.vsStateName !== 'Assam'"
                          >
                          <option value="">Select</option>
                        </select>
                        <div class="form-control-line"></div>
                      </div>
                    </div>

            <!-- ******************************************************************************* -->
            <!-- End  of council and  assembly constituency -->
            <!-- ******************************************************************************* -->


            <div class="form-group required">
                <label for="resAddress" class="col-sm-2 control-label required">Address/Street/Building</label>
                <div class="col-sm-10">
                    <input capitalize name="resAddress" id="resAddress" ng-model="vm.currentForm.resAddressDetail.address" type="text" class="form-control" id="resAddress" placeholder="" required>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group required">
                <label for="resCity" class="col-sm-2 control-label required">City/Village Name</label>
                <div class="col-sm-10">
                    <input capitalize name="resCity" id="resCity" ng-model="vm.currentForm.resAddressDetail.cityName" type="text" class="form-control" id="resCity" placeholder="" required>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group " ng-if="vm.currentForm.accessObj.postOfficeInvolved===1">
                <label for="resPostOffice" class="col-sm-2 control-label">Post Office</label>
                <div class="col-sm-10">
                    <input capitalize ng-model="vm.currentForm.resAddressDetail.postOffice" type="text" class="form-control" id="resPostOffice" placeholder="">
                    <div class="form-control-line"></div>
                </div>
            </div>

            <div class="form-group " ng-if="vm.currentForm.accessObj.policeStationInvolved===1">
                    <label for="perPoliceStation" class="col-sm-2 control-label">Police Station</label>
                    <div class="col-sm-10">
                        <input capitalize ng-model="vm.currentForm.resAddressDetail.policeStation" type="text" class="form-control" id="perPoliceSation" name="perPoliceStation" placeholder="">
                        <div class="form-control-line"></div>
                    </div>
            </div>
            <div class="form-group required" ng-if="vm.currentForm.accessObj.policeStationInvolved===1">
                <label for="resPincode" class="col-sm-2 control-label required">Pincode </label>
                <div class="col-sm-10">
                    <input name="resPincode" id="resPincode" ng-model="vm.currentForm.resAddressDetail.pincode" type="text" class="form-control" id="resPincode" placeholder="" required>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group required">
                <label for="resNationality" class="col-sm-2 control-label required">Nationality </label>
                <div class="col-sm-10">
                    <input capitalize name="resNationality" id="resNationality" ng-model="vm.currentForm.resAddressDetail.nationality" type="text" class="form-control" id="resNationality" placeholder="" required>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div ng-if="vm.currentForm.showSameAddressCheck" class="checkbox checkbox-styled">
                <label>
                    <input ng-model="vm.currentForm.isPermanentSameAsRes" type="checkbox" ng-change="addressSameAsResidentialHandler()"> <span>Is Permanent Address same as Residential Address?</span>
                </label>
            </div>
            <div ng-show="!vm.currentForm.isPermanentSameAsRes">
                <p class="lead">
                    <ins>Permanent Address</ins>
                </p>
                <input ng-hide="true" ng-model="vm.currentForm.perAddressDetail.addressId" type="hidden" id="perAddressId" placeholder="">
                <input ng-hide="true" ng-model="vm.currentForm.perAddressDetail.addressType" type="hidden" id="perAddressId" placeholder="">
                <div class="form-group required">
                    <label for="perCountry" class="col-sm-2 control-label required">Country</label>
                    <div class="col-sm-10">
                        <select name="perCountry" id="perCountry" ng-options="country.vsCountryName for country in vm.currentForm.percountryList track by country.pklCountryId" ng-model="vm.currentForm.perAddressDetail.country" class="form-control" ng-change="vm.loadPerState(vm.currentForm.perAddressDetail.country)" ng-required="!vm.currentForm.isPermanentSameAsRes">
                        </select>
                        <div class="form-control-line"></div>
                    </div>
                </div>
                <div class="form-group required">
                    <label for="perState" class="col-sm-2 control-label required">State</label>
                    <div class="col-sm-10">
                        <select name="perState" id="perState" ng-options="state.vsStateName for state in vm.currentForm.perstateList track by state.pklStateId" ng-model="vm.currentForm.perAddressDetail.state" class="form-control" ng-change="vm.loadPerDistrict(vm.currentForm.perAddressDetail.state)" ng-required="!vm.currentForm.isPermanentSameAsRes">
                        </select>
                        <div class="form-control-line"></div>
                    </div>
                </div>
                <div class="form-group required">
                    <label for="perDistrict" class="col-sm-2 control-label required">District</label>
                    <div class="col-sm-10">
                        <select name="perDistrict" id="perDistrict" ng-options="district.vcDistName for district in vm.currentForm.perdistrictList track by district.pkDistId" ng-model="vm.currentForm.perAddressDetail.district" class="form-control" ng-change="vm.loadPerTaluka(vm.currentForm.perAddressDetail.district)" ng-required="!vm.currentForm.isPermanentSameAsRes">
                        </select>
                        <div class="form-control-line"></div>
                    </div>
                </div>
                <div class="form-group required"> 
                    <label for="perRuralUrban" class="col-sm-2 control-label required">Rural/Urban </label>
                    <div class="col-sm-10">
                        <label class="radio-inline radio-styled">
                            <input ng-model="vm.currentForm.perAddressDetail.areaType" type="radio" name="perRularUrbanOptions" value="RURAL" ng-required="!vm.currentForm.isPermanentSameAsRes"> <span>Rural</span>
                        </label>
                        <label class="radio-inline radio-styled">
                            <input ng-model="vm.currentForm.perAddressDetail.areaType" type="radio" name="perRularUrbanOptions" value="URBAN" ng-required="!vm.currentForm.isPermanentSameAsRes"> <span>Urban</span>
                        </label>
                    </div>
                </div>
                   <!-- ******************************************************************************* -->
            <!-- for ulb and block -->
            <!-- ******************************************************************************* -->
                    <!--Block-->
                    <div class="form-group required" ng-if="vm.currentForm.perAddressDetail.areaType==='RURAL'">
                      <label class="col-sm-2 control-label">
                        <strong class="text-accent-dark text-bold  ">Block
                        </strong>
                      </label>
                      <div class="col-sm-10">
                        <select class="form-control" ng-options="taluka.pkTalId as taluka.vcTalName for taluka in vm.currentForm.pertalukaList"
                          ng-model="vm.currentForm.perAddressDetail.talukaId"
                          
                          >
                          <option value="">Select</option>
                        </select>
                        <div class="form-control-line"></div>
                      </div>
                    </div>

                    <div class="form-group required" ng-if="vm.currentForm.perAddressDetail.areaType==='URBAN'">
                      <label class="col-sm-2 control-label">
                        <strong class="text-accent-dark text-bold  ">ULB
                        </strong>
                      </label>
                      <div class="col-sm-10">
                        <select class="form-control" ng-options="ulb.ulbId as ulb.ulbName for ulb in vm.currentForm.perulbList"
                          ng-model="vm.currentForm.perAddressDetail.ulbId">
                          <option value="">Select</option>
                        </select>
                        <div class="form-control-line"></div>
                      </div>
                    </div>

            <!-- ******************************************************************************* -->
            <!-- End of ulb and block -->
            <!-- ******************************************************************************* -->



            <!-- ******************************************************************************* -->
            <!-- Start of council and  assembly constituency -->
            <!-- ******************************************************************************* -->

                       <!-- Council constituency -->
                    <div class="form-group required" ng-if="vm.currentForm.accessObj.locasabhaConstituencyInvolved===1">
                      <label class="col-sm-2 control-label">
                        <strong class="text-accent-dark text-bold"> Council constituency 
                        </strong>
                      </label>
                      <div class="col-sm-10">
                        <select class="form-control" ng-options="councilC.loksabhaId as councilC.loksabhaName for councilC in vm.currentForm.loksabhaArr"
                          ng-model="vm.currentForm.perAddressDetail.councilId" ng-change="vm.getAssemlyArr(vm.currentForm.perAddressDetail.councilId)">
                          <option value="">Select</option>
                        </select>
                        <div class="form-control-line"></div>
                      </div>
                    </div>

                    <!-- Assembly constituency -->
                    <div class="form-group required" ng-if="vm.currentForm.accessObj.assemblyConstituencyInvolved===1">
                      <label class="col-sm-2 control-label">
                        <strong class="text-accent-dark text-bold">Assembly constituency
                        </strong>
                      </label>
                      <div class="col-sm-10">
                        <select class="form-control" ng-options="assemblyC.assemblyId as assemblyC.assemblyName for assemblyC in vm.currentForm.assemblyArr"
                          ng-model="vm.currentForm.perAddressDetail.assemblyId">
                          <option value="">Select</option>
                        </select>
                        <div class="form-control-line"></div>
                      </div>
                    </div>

            <!-- ******************************************************************************* -->
            <!-- End  of council and  assembly constituency -->
            <!-- ******************************************************************************* -->

                <div class="form-group required">
                    <label for="perAddress" class="col-sm-2 control-label required">Address/Street/Building</label>
                    <div class="col-sm-10">
                        <input capitalize ng-model="vm.currentForm.perAddressDetail.address" type="text" class="form-control" id="perAddress" name="perAddress" placeholder="" ng-required="!vm.currentForm.isPermanentSameAsRes">
                        <div class="form-control-line"></div>
                    </div>
                </div>
                <div class="form-group required" >
                    <label for="perCity" class="col-sm-2 control-label required">City/Village Name</label>
                    <div class="col-sm-10">
                        <input capitalize ng-model="vm.currentForm.perAddressDetail.cityName" type="text" class="form-control" id="perCity" name="perCity" placeholder="" ng-required="!vm.currentForm.isPermanentSameAsRes">
                        <div class="form-control-line"></div>
                    </div>
                </div>
                <div class="form-group " ng-if="vm.currentForm.accessObj.postOfficeInvolved===1">
                    <label for="perPostOffice" class="col-sm-2 control-label">Post Office</label>
                    <div class="col-sm-10">
                        <input capitalize ng-model="vm.currentForm.perAddressDetail.postOffice" type="text" class="form-control" id="perPostOffice" name="perPostOffice" placeholder="">
                        <div class="form-control-line"></div>
                    </div>
                </div>

                <div class="form-group " ng-if="vm.currentForm.accessObj.policeStationInvolved===1">
                    <label for="perPoliceStation" class="col-sm-2 control-label">Police Station</label>
                    <div class="col-sm-10">
                        <input capitalize ng-model="vm.currentForm.perAddressDetail.policeStation" type="text" class="form-control" id="perPoliceSation" name="perPoliceStation" placeholder="">
                        <div class="form-control-line"></div>
                    </div>
                </div>

                <div class="form-group required">
                    <label for="perPincode" class="col-sm-2 control-label required">Pincode </label>
                    <div class="col-sm-10">
                        <input ng-model="vm.currentForm.perAddressDetail.pincode" type="text" class="form-control" id="perPincode" name="perPincode" placeholder="" ng-required="!vm.currentForm.isPermanentSameAsRes">
                        <div class="form-control-line"></div>
                    </div>
                </div>
                <div class="form-group required">
                    <label for="perNationality" class="col-sm-2 control-label required">Nationality </label>
                    <div class="col-sm-10">
                        <input capitalize ng-model="vm.currentForm.perAddressDetail.nationality" type="text" class="form-control" id="perNationality" name="perNationality" placeholder="" ng-required="!vm.currentForm.isPermanentSameAsRes">
                        <div class="form-control-line"></div>
                    </div>
                </div>
            </div>
            <div class="form-group required">
                <label for="correspondence" class="col-sm-2 control-label required">Address for correspondence </label>
                <div class="col-sm-10">
                    <label class="radio-inline radio-styled">
                        <input ng-model="vm.currentForm.correspondenceAddress" type="radio" name="correspondenceOption" value="RESIDENTIAL" required> <span>Residential</span>
                    </label>
                    <label class="radio-inline radio-styled">
                        <input ng-model="vm.currentForm.correspondenceAddress" type="radio" name="correspondenceOption" value="PERMANENT" required> <span>Permanent</span>
                    </label>

                </div>
            </div>
            <div ng-if="!vm.currentForm.addressStatus" class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    <button type="button" ng-click="vm.saveAddressDetails(addressForm)" class="btn btn-primary col-sm-3">Save</button>
                </div>
            </div>
            <div ng-if="vm.currentForm.addressStatus" class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    <button type="button" ng-click="vm.updateAddressDetails(addressForm)" class="btn btn-primary col-sm-3">Update</button>
                </div>
            </div>
        </form>
    </div>
</div>
