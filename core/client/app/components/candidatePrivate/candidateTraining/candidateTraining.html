<div class="card card-outlined style-primary" id="candidateAddressScroll">
    <div class="card-head">
        <header><i class="fa fa-fw fa-tag"></i>Candidate Short1 Term Skill Training Details (optional)</header>
    </div>
    <div class="card-body">
        <div>
            <div class="alert alert-callout alert-danger" role="alert" ng-if="vm.currentForm.trainingListStatus === 'error'">
                <strong class="text-danger text-bold text-lg">Error!!!</strong>
                <h4 ng-bind="vm.currentForm.message"></h4>
                <p class="text-danger" ng-show="trainingForm.trainingSector.$error.required">Training Sector is required.</p>
                <p class="text-danger" ng-show="trainingForm.trainingSector.$error.required">Training Sector is required.</p>
                <p class="text-danger" ng-show="trainingForm.courseName.$error.required">Course Name is required.</p>
                <p class="text-danger" ng-show="trainingForm.courseYear.$error.required">Course Year is required.</p>
                <p class="text-danger" ng-show="trainingForm.courseDuration.$error.required">Course Duration is required.</p>
                <p class="text-danger" ng-show="trainingForm.coursePrescriber.$error.required">Course Prescribed By is required.</p>
                <p class="text-danger" ng-show="trainingForm.trainingFunded.$error.required">Training Funded By is required.</p>
                <p class="text-danger" ng-show="trainingForm.certifyingAuthority.$error.required">Certifying Authority/Agency Name is required.</p>
            </div>
            <div class="alert alert-callout alert-danger" role="alert" ng-if="vm.currentForm.trainingStatus === 'error'">
                <strong class="text-danger text-bold text-lg">Error!!!</strong>
                <h4 ng-bind="vm.currentForm.message"></h4>
            </div>
            <!--Success messages -->
            <div class="alert alert-callout alert-success" role="alert" ng-if="vm.currentForm.trainingStatus === 'success'">
                <strong class="text-success text-bold text-lg">Success!!!</strong>
                <h4 ng-bind="vm.currentForm.message"></h4>
            </div>
            <div class="alert alert-callout alert-info" role="alert" ng-if="vm.currentForm.trainingListStatus === 'success'">
                <strong class="text-info text-bold text-lg">Info!!!</strong>
                <h4 ng-bind="vm.currentForm.message"></h4>
                <p class="text-info">Please do not forget to click on Save/ Update Button</p>
            </div>
        </div>
        <form name="trainingForm" class="form-horizontal">
            <input ng-hide="true" ng-model="vm.currentForm.candidateTrainingDetail.candidateTrainingId" type="hidden" id="candidateTrainingId" placeholder="">
            <div class="form-group">
                <label for="trainingSector" class="col-sm-2 control-label">Sector Name</label>
                <div class="col-sm-6">
                    <input capitalize id="trainingSector" name="trainingSector" ng-model="vm.currentForm.trainingDetail.trainingSector" type="text" class="form-control" placeholder="" required>
                    <p class="help-block text-muted">e.g. Apparel, Electronics, Telecom, Furniture & Fittings etc</p>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group">
                <label for="courseName" class="col-sm-2 control-label">Course Name</label>
                <div class="col-sm-6">
                    <input id="courseName" capitalize name="courseName" ng-model="vm.currentForm.trainingDetail.courseName" type="text" class="form-control" placeholder="" required>
                    <p class="help-block text-muted">e.g. Hand Embroiderer, Sewing Machine Operator </p>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group">
                <label for="courseName" class="col-sm-2 control-label">Year of Training</label>
                <div class="col-sm-3">
                    <!-- <input id="courseYear" name="courseYear" ng-model="vm.currentForm.trainingDetail.courseYear" type="number" class="form-control" placeholder="" restrict="reject"  min="2016" max = "2017" clean="true" required> -->

                    <select name="courseYear" id="courseYear" ng-options="year for year in vm.yearList track by year" ng-model="vm.currentForm.trainingDetail.courseYear" class="form-control" required>
                    </select>


                    <p class="help-block text-muted">e.g. 2010</p>
                    <div class="form-control-line"></div>
                </div>
                <label for="courseDuration" class="col-sm-2 control-label">Course Duration (Days)</label>
                <div class="col-sm-3">
                    <input id="courseDuration" name="courseDuration" ng-model="vm.currentForm.trainingDetail.courseDuration" type="number" class="form-control" placeholder="" required>
                    <p class="help-block text-muted">e.g. 45 (in number of Days)</p>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group">
                <label for="coursePrescriber" class="col-sm-2 control-label">Course Prescribed By</label>
                <div class="col-sm-6">
                    <input id="coursePrescriber" capitalize name="coursePrescriber" ng-model="vm.currentForm.trainingDetail.coursePrescriber" type="text" class="form-control" placeholder="" required>
                    <p class="help-block text-muted">e.g. SSC, MES or Name of Agency etc</p>
                    <div class="form-control-line"></div>
                </div>
            </div>
            <div class="form-group">
                <label for="trainingFunded" class="col-sm-2 control-label">Training Funded By</label>
                <div class="col-sm-6">
                    <select name="trainingFunded" id="trainingFunded" ng-model="vm.currentForm.trainingDetail.trainingFunded" class="form-control" required>
                        <option ng-repeat="fundDetail in vm.currentForm.fundDetails" value="{{fundDetail}}">{{fundDetail}}</option>
                    </select>
                    
                    <input 
                    ng-if="vm.currentForm.trainingDetail.trainingFunded === 'Other'"
                    id="trainingFunded" capitalize name="trainingFunded" ng-model="vm.currentForm.trainingDetail.trainingFunded" type="text" class="form-control" placeholder="" required>
                    <p class="help-block text-muted">e.g. SELF, GOVTERNMENT DEPARTMENT or Name of Organization etc</p>
                    <div class="form-control-line"></div>
                </div>
            </div>

            <div class="form-group">
                <label for="trainingFunded" class="col-sm-2 control-label">Have you received certificate?</label>
                <div class="col-sm-6">
                    <label class="radio-inline radio-styled">
                        <input type="radio" ng-model="vm.currentForm.trainingDetail.isCertificateReceived"  value="Yes" required><span>Yes</span>
                        </label>
                        <label class="radio-inline radio-styled">
                            <input type="radio" ng-model="vm.currentForm.trainingDetail.isCertificateReceived"  value="No" required><span> No</span>
                        </label>

                    <div class="form-control-line"></div>
                </div>
            </div>


             <!-- <div class="form-group">
                    <label for="certifyingAuthority" class="col-sm-2 control-label"></label>
                    <div class="col-sm-6 checkbox checkbox-styled">
                    <label>
                        <input ng-model="vm.currentForm.trainingDetail.isCertificateReceived" type="checkbox" ng-true-value="'Yes'" ng-false-value="'No'"> <span>Have you received certificate?</span>
                        </label>
                    </div>
                </div>  -->

            <!-- <div class="col-sm-8 checkbox checkbox-styled">
            
                <label>
                    <input vm.currentForm.trainingDetail.isCertificateReceived " type="checkbox " ng-true-value=" 'Yes' " ng-false-value=" 'No' "> <span>Have you received certificate?</span>
                </label>
            </div> -->
                <div class="form-group">
                    <label for="certifyingAuthority " class="col-sm-2 control-label ">Certification Issued By</label>
                    <div class="col-sm-8 ">
                        <input id="certifyingAuthority " capitalize name="certifyingAuthority " ng-model="vm.currentForm.trainingDetail.certifyingAuthority " type="text " class="form-control " placeholder=" " ng-required="vm.currentForm.trainingDetail.isCertificateReceived ">
                        <p class="help-block text-muted ">e.g. GOVTERNMENT DEPARTMENT or Name of Organization etc</p>
                        <div class="form-control-line "></div>
                    </div>
                </div>
                <!--  <div>
                                               <label for="certificateReceived " class="col-sm-6 control-label ">Certificate Received
                                               <div class="col-sm-6 ">
                                               <input ng-model="vm.currentForm.trainingDetail.isCertificateReceived " type="checkbox " ng-true-value="Yes " ng-false-value="No ">Have you received certificate?
                                               </div>
                                               </label>
                                           </div> -->
                <div class="form-group ">
                    <div class="col-sm-offset-2 col-sm-10 ">
                        <button class="btn btn-default col-sm-3 " type="button " ng-click="vm.addToTrainingList(trainingForm) ">Add</button>
                    </div>
                </div>
                <table class="table table-bordered table-responsive ">
                    <thead>
                        <tr>
                            <th>SR. No.</th>
                            <th>Training Sector - Course</th>
                            <th>Year - Duration</th>
                            <th>Course Prescribed - Funded By</th>
                            <th>Certificate Received - Issued By</th>
                            <th> </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="trainingDetail in vm.currentForm.trainingDetailList track by $index ">
                            <td>{{$index + 1}}</td>
                            <td>{{trainingDetail.trainingSector}}-{{trainingDetail.courseName}}</td>
                            <td>{{trainingDetail.courseYear}}-{{trainingDetail.courseDuration}}</td>
                            <td>{{trainingDetail.coursePrescriber}}-{{trainingDetail.trainingFunded}}</td>
                            <td>{{trainingDetail.isCertificateReceived}}-{{trainingDetail.certifyingAuthority}}</td>
                            <td>
                                <button type="button " class="btn btn-danger " ng-click="vm.removeTrainingDetail(trainingDetail) "> <i class="glyphicon glyphicon-trash " aria-hidden="true "></i> Remove</button>
                            </td>
                        </tr>
                        <tr ng-if="vm.currentForm.trainingDetailList.length===0 ">
                            <td colspan="6 "> No record found </td>
                        </tr>
                    </tbody>
                </table>
                <div ng-if="!vm.currentForm.candidateTrainingDetail.candidateTrainingId " class="form-group ">
                    <div class="col-sm-offset-2 col-sm-10 ">
                        <button class="btn btn-primary col-sm-3 " type="button " ng-click="vm.saveCandidateTrainingDetails() ">Save</button>
                    </div>
                </div>
                <div ng-if="vm.currentForm.candidateTrainingDetail.candidateTrainingId " class="form-group ">
                    <div class="col-sm-offset-2 col-sm-10 ">
                        <button class="btn btn-primary col-sm-3 " type="button " ng-click="vm.updateCandidateTrainingDetails() ">Update</button>
                    </div>
                </div>
            </form>
    </div>
</div>
