/**
 * Created by vijayc.
 */
class candidateTrainingController {
    /*@ngInject*/
    constructor($filter, $scope, pouchDB, RestangularCRMS,
        $translate, $translatePartialLoader, $state, $stateParams, User, $anchorScroll, $location) {
        this.self = this;
        this.trainingRest = RestangularCRMS.all("training/getAll");
        this.trainingDetailSaveRest = RestangularCRMS.all("trainingdetail/save");
        this.trainingDetailGetRest = RestangularCRMS.all("trainingdetail/get");
        this.trainingDetailUpdateRest = RestangularCRMS.all("trainingdetail/update");

        this.$anchorScroll = $anchorScroll;
        this.$location = $location;
        this.currentForm = {};
        this.currentForm.candidateId = this.candidateId;
        this.filter = $filter;
        this.user = User;

        this.yearList = [new Date().getFullYear(), new Date().getFullYear()-1];

        this.currentForm.candidateTrainingDetail = {};
        this.currentForm.trainingDetail = {};
        this.getTrainingDetails(this.currentForm.candidateId);
    }

   
        /*************start : Disability Details Methods**********************************************************************************/
    getTrainingDetails(candidateId) {
        this.currentForm.trainingListStatus = "init";
        this.currentForm.trainingStatus = "init";
        this.currentForm.message = "";
        this.currentForm.trainingDetailList = [];
        let postParam = {};
        postParam.candidateId = candidateId;
        this.trainingDetailGetRest.post({ "postParam": postParam })
            .then(function(data) {
              
                this.currentForm.fundDetails = data.fundby.map(function(item){
                    return item.vsFundBy;
                    
                });
                console.log(this.currentForm.fundDetails);
                if (data !== undefined && data !== null && !angular.equals({}, data) && data.status === "success") {
                    if (data.objArr !== undefined && data.objArr !== null) {
                        this.currentForm.candidateTrainingDetail.candidateTrainingId = data.objArr.candidateTrainingId;
                        if (data.objArr.trainings !== null) {
                            this.currentForm.trainingDetailList = JSON.parse(data.objArr.trainings);
                        }
                    }
                } else {
                    this.currentForm.trainingStatus = "init";
                    this.currentForm.message = data.message;
                }
            }.bind(this.self));
        this.currentForm.showCompleteForm = true;
        this.currentForm.showGrid = false;
    }

    addToTrainingList(trainingForm) {
        this.currentForm.trainingListStatus = "init";
        this.currentForm.trainingStatus = "init";
        this.currentForm.message = "";
        if (trainingForm.$valid) {
            this.currentForm.trainingDetailList.push(this.currentForm.trainingDetail);
            this.currentForm.trainingListStatus = "success";
            this.currentForm.message = "Record added in list.";
            this.clearTrainingDetailForm();
            this.$anchorScroll('candidateTrainingScroll');
        } else {
            this.currentForm.trainingListStatus = "error";
            this.currentForm.message = "Please fill all required fields.";
            this.$anchorScroll('candidateTrainingScroll');
        }
    }

    removeTrainingDetail(training) {
        console.log(this.currentForm.trainingDetailList.length);
        this.currentForm.trainingDetailList.splice(this.currentForm.trainingDetailList.indexOf(training), 1);
        this.currentForm.trainingListStatus = "success";
        this.currentForm.message = "Record removed from list.";
        this.$anchorScroll('candidateTrainingScroll');
    }
    clearTrainingDetailForm() {
        this.currentForm.trainingDetail = {};
    }
    saveCandidateTrainingDetails() {
        this.currentForm.trainingListStatus = "init";
        this.currentForm.trainingStatus = "init";
        this.currentForm.message = "";
        this.currentForm.candidateTrainingDetail.candidateId = this.currentForm.candidateId;

        if (this.currentForm.trainingDetailList.length > 0) {
            this.currentForm.candidateTrainingDetail.trainings = this.currentForm.trainingDetailList;
            console.log(this.currentForm.candidateTrainingDetail);
            this.trainingDetailSaveRest.post({ "trainingDetail": this.currentForm.candidateTrainingDetail })
                .then(function(data) {
                    if (data !== undefined && data !== null && !angular.equals({}, data) && data.status === "success") {
                        this.currentForm.trainingStatus = data.status;
                        this.currentForm.message = data.message;
                        this.currentForm.candidateTrainingDetail.candidateTrainingId = data.objArr.insertId;
                        this.validate({ candidateId: this.currentForm.candidateId });
                        this.$anchorScroll('candidateTrainingScroll');
                    } else {
                        this.currentForm.trainingStatus = "error";
                        this.currentForm.message = data.message;
                        this.$anchorScroll('candidateTrainingScroll');
                    }
                }.bind(this.self));
        } else {
            this.currentForm.trainingStatus = "error";
            this.currentForm.message = "Candidate training: Please add your training detail";
            this.$anchorScroll('candidateTrainingScroll');
        }


    }

    updateCandidateTrainingDetails() {
            this.currentForm.trainingListStatus = "init";
            this.currentForm.trainingStatus = "init";
            this.currentForm.message = "";
            this.currentForm.candidateTrainingDetail.candidateId = this.currentForm.candidateId;

            if (this.currentForm.trainingDetailList.length > 0) {
                this.currentForm.candidateTrainingDetail.trainings = this.currentForm.trainingDetailList;
                this.trainingDetailUpdateRest.post({ "trainingDetail": this.currentForm.candidateTrainingDetail })
                    .then(function(data) {
                        if (data !== undefined && data !== null && !angular.equals({}, data) && data.status === "success") {
                            this.currentForm.trainingStatus = data.status;
                            this.currentForm.message = data.message;
                            this.$anchorScroll('candidateTrainingScroll');
                        } else {
                            this.currentForm.trainingStatus = "error";
                            this.currentForm.message = data.message;
                            this.$anchorScroll('candidateTrainingScroll');
                        }
                    }.bind(this.self));
            } else {
                this.currentForm.trainingStatus = "error";
                this.currentForm.message = "Candidate training : Please add your training detail";
                this.$anchorScroll('candidateTrainingScroll');
            }
        }
        /*************end : Disability Details Methods**********************************************************************************/


    

}
export default candidateTrainingController;
